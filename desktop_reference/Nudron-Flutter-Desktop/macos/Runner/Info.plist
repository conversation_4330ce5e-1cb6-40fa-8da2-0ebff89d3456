<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <!-- Required: Localization -->
    <key>CFBundleDevelopmentRegion</key>
    <string>$(DEVELOPMENT_LANGUAGE)</string>

    <!-- Required: App display name -->
    <key>CFBundleDisplayName</key>
    <string>Water Meter</string>

    <!-- Required: Executable name -->
    <key>CFBundleExecutable</key>
    <string>$(EXECUTABLE_NAME)</string>

    <!-- Required: App identifier -->
    <key>CFBundleIdentifier</key>
    <string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>

    <!-- Required: Info.plist version -->
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>

    <!-- Required: App name -->
    <key>CFBundleName</key>
    <string>$(PRODUCT_NAME)</string>

    <!-- Required: App package type -->
    <key>CFBundlePackageType</key>
    <string>APPL</string>

    <!-- Required: App version (short string) -->
    <key>CFBundleShortVersionString</key>
    <string>$(FLUTTER_BUILD_NAME)</string>

    <!-- Required: App version (build number) -->
    <key>CFBundleVersion</key>
    <string>$(FLUTTER_BUILD_NUMBER)</string>

    <!-- Required: App category -->
    <key>LSApplicationCategoryType</key>
    <string>public.app-category.utilities</string>

    <!-- Required: Minimum supported macOS version -->
    <key>LSMinimumSystemVersion</key>
    <string>$(MACOSX_DEPLOYMENT_TARGET)</string>

    <!-- Required: Copyright -->
    <key>NSHumanReadableCopyright</key>
    <string>$(PRODUCT_COPYRIGHT)</string>

    <!-- Required: Principal class -->
    <key>NSPrincipalClass</key>
    <string>NSApplication</string>

    <!-- Recommended: Document types you support -->
    <key>CFBundleDocumentTypes</key>
    <array>
        <dict>
            <key>CFBundleTypeName</key>
            <string>Excel Document</string>
            <key>LSHandlerRank</key>
            <string>Owner</string>
            <key>LSItemContentTypes</key>
            <array>
                <string>com.microsoft.excel.xls</string>
                <string>org.openxmlformats.spreadsheetml.sheet</string>
            </array>
        </dict>
        <dict>
            <key>CFBundleTypeName</key>
            <string>PNG Image</string>
            <key>LSHandlerRank</key>
            <string>Owner</string>
            <key>LSItemContentTypes</key>
            <array>
                <string>public.png</string>
            </array>
        </dict>
    </array>

    <!-- Recommended: Custom URL schemes (if used) -->
    <key>CFBundleURLTypes</key>
    <array>
        <dict>
            <key>CFBundleURLName</key>
            <string>com.nudron.watermeter2</string>
            <key>CFBundleURLSchemes</key>
            <array>
                <string>otpauth</string>
            </array>
        </dict>
    </array>

    <!-- Required: Usage descriptions for privacy-sensitive APIs -->
    <!-- Only include these if your app actually uses them! -->
    <key>NSDocumentsFolderUsageDescription</key>
    <string>We need access to save and open exported Excel files.</string>
    <key>NSPhotoLibraryUsageDescription</key>
    <string>We need access to your photo library to save chart images if required.</string>

    <!-- Recommended: Open documents in place (for sandboxed apps) -->
    <key>LSSupportsOpeningDocumentsInPlace</key>
    <true/>
</dict>
</plist>
