<component name="libraryTable">
  <library name="Dart Packages" type="DartPackagesLibraryType">
    <properties>
      <option name="packageNameToDirsMap">
        <entry key="archive">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/archive-3.6.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="args">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/args-2.7.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="async">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/async-2.13.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="bloc">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/bloc-8.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="boolean_selector">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="characters">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/characters-1.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="checked_yaml">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="cli_util">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/cli_util-0.4.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="clipboard">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/clipboard-0.1.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="clock">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/clock-1.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="collection">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/collection-1.19.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="connectivity_plus">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="connectivity_plus_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="console">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/console-4.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="crypto">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="cupertino_icons">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="dbus">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib" />
            </list>
          </value>
        </entry>
        <entry key="device_frame">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="device_info_plus">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/device_info_plus-11.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="device_info_plus_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="device_preview">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="equatable">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="excel">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/excel-4.0.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="fake_async">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/fake_async-1.3.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="ffi">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="file">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/file-7.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter">
          <value>
            <list>
              <option value="$USER_HOME$/development/flutter/packages/flutter/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_bloc">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_bloc-8.1.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_breadcrumb">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_breadcrumb-1.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_launcher_icons">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.14.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_localizations">
          <value>
            <list>
              <option value="$USER_HOME$/development/flutter/packages/flutter_localizations/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_plugin_android_lifecycle">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_screenutil">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_screenutil-5.9.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_secure_storage">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_secure_storage_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_secure_storage_macos">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_secure_storage_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_secure_storage_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_secure_storage_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_svg">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_svg-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_switch">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_switch-0.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_test">
          <value>
            <list>
              <option value="$USER_HOME$/development/flutter/packages/flutter_test/lib" />
            </list>
          </value>
        </entry>
        <entry key="flutter_web_plugins">
          <value>
            <list>
              <option value="$USER_HOME$/development/flutter/packages/flutter_web_plugins/lib" />
            </list>
          </value>
        </entry>
        <entry key="freezed_annotation">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/freezed_annotation-2.4.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="get_it">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/get_it-7.7.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="google_fonts">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="http">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/http-1.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="http_parser">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="image">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/image-4.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="in_app_update">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/in_app_update-4.2.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="intl">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/intl-0.20.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="js">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/js-0.6.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="json_annotation">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="leak_tracker">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9/lib" />
            </list>
          </value>
        </entry>
        <entry key="leak_tracker_flutter_testing">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/lib" />
            </list>
          </value>
        </entry>
        <entry key="leak_tracker_testing">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="loading_animation_widget">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="local_auth">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/local_auth-2.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="local_auth_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/local_auth_android-1.0.49/lib" />
            </list>
          </value>
        </entry>
        <entry key="local_auth_darwin">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="local_auth_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/lib" />
            </list>
          </value>
        </entry>
        <entry key="local_auth_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/lib" />
            </list>
          </value>
        </entry>
        <entry key="matcher">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/matcher-0.12.17/lib" />
            </list>
          </value>
        </entry>
        <entry key="material_color_utilities">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="meta">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/meta-1.16.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="msix">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/msix-3.16.9/lib" />
            </list>
          </value>
        </entry>
        <entry key="nested">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/nested-1.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="nm">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/nm-0.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="open_file">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/open_file-3.5.10/lib" />
            </list>
          </value>
        </entry>
        <entry key="open_file_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/open_file_android-1.0.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="open_file_ios">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/open_file_ios-1.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="open_file_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/open_file_linux-0.0.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="open_file_mac">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/open_file_mac-1.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="open_file_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/open_file_platform_interface-1.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="open_file_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/open_file_web-0.0.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="open_file_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/open_file_windows-0.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="package_config">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/package_config-2.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="path">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path-1.9.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_parsing">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_parsing-1.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider-2.1.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_foundation">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="path_provider_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="pedantic">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/pedantic-1.11.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/permission_handler-11.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_apple">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_html">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="permission_handler_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="petitparser">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="pin_code_fields">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/pin_code_fields-8.0.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="pin_input_text_field">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/pin_input_text_field-4.5.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="platform">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/platform-3.1.6/lib" />
            </list>
          </value>
        </entry>
        <entry key="plugin_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib" />
            </list>
          </value>
        </entry>
        <entry key="provider">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/provider-6.1.5/lib" />
            </list>
          </value>
        </entry>
        <entry key="pub_semver">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/pub_semver-2.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="screen_retriever">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/screen_retriever-0.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="screen_retriever_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/screen_retriever_linux-0.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="screen_retriever_macos">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/screen_retriever_macos-0.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="screen_retriever_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/screen_retriever_platform_interface-0.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="screen_retriever_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/screen_retriever_windows-0.2.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="screenshot">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/screenshot-3.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_foundation">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="shared_preferences_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="sky_engine">
          <value>
            <list>
              <option value="$USER_HOME$/development/flutter/bin/cache/pkg/sky_engine/lib" />
            </list>
          </value>
        </entry>
        <entry key="sms_autofill">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/sms_autofill-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="source_span">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="stack_trace">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="stream_channel">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="string_scanner">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="super_tooltip">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/super_tooltip-2.0.9/lib" />
            </list>
          </value>
        </entry>
        <entry key="syncfusion_flutter_charts">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-29.2.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="syncfusion_flutter_core">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-29.2.7/lib" />
            </list>
          </value>
        </entry>
        <entry key="term_glyph">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="test_api">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/test_api-0.7.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="timeago">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="typed_data">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_android">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_ios">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_linux">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_macos">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="url_launcher_windows">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="vector_graphics">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/vector_graphics-1.1.18/lib" />
            </list>
          </value>
        </entry>
        <entry key="vector_graphics_codec">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13/lib" />
            </list>
          </value>
        </entry>
        <entry key="vector_graphics_compiler">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.17/lib" />
            </list>
          </value>
        </entry>
        <entry key="vector_math">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib" />
            </list>
          </value>
        </entry>
        <entry key="vibration">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/vibration-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="vibration_platform_interface">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/vibration_platform_interface-0.0.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="vm_service">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/vm_service-15.0.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="web">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/web-1.1.1/lib" />
            </list>
          </value>
        </entry>
        <entry key="win32">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/win32-5.13.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="win32_registry">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/win32_registry-2.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="window_manager">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/window_manager-0.4.3/lib" />
            </list>
          </value>
        </entry>
        <entry key="xdg_directories">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="xml">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/xml-6.5.0/lib" />
            </list>
          </value>
        </entry>
        <entry key="yaml">
          <value>
            <list>
              <option value="$USER_HOME$/.pub-cache/hosted/pub.dev/yaml-3.1.3/lib" />
            </list>
          </value>
        </entry>
      </option>
    </properties>
    <CLASSES>
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/archive-3.6.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/args-2.7.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/async-2.13.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/bloc-8.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/characters-1.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/checked_yaml-2.0.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/cli_util-0.4.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/clipboard-0.1.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/clock-1.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/collection-1.19.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/console-4.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/crypto-3.0.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/dbus-0.7.11/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/device_frame-1.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/device_info_plus-11.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/device_preview-1.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/equatable-2.0.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/excel-4.0.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/fake_async-1.3.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/ffi-2.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/file-7.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_bloc-8.1.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_breadcrumb-1.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.14.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_screenutil-5.9.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_svg-2.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/flutter_switch-0.3.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/freezed_annotation-2.4.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/get_it-7.7.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/google_fonts-6.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/http-1.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/http_parser-4.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/image-4.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/in_app_update-4.2.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/intl-0.20.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/js-0.6.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/loading_animation_widget-1.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/local_auth-2.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/local_auth_android-1.0.49/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/local_auth_darwin-1.4.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/local_auth_platform_interface-1.0.10/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/local_auth_windows-1.0.11/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/matcher-0.12.17/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/meta-1.16.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/msix-3.16.9/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/nested-1.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/nm-0.5.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/open_file-3.5.10/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/open_file_android-1.0.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/open_file_ios-1.0.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/open_file_linux-0.0.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/open_file_mac-1.0.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/open_file_platform_interface-1.0.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/open_file_web-0.0.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/open_file_windows-0.0.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/package_config-2.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path-1.9.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_parsing-1.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider-2.1.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/pedantic-1.11.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/permission_handler-11.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/petitparser-6.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/pin_code_fields-8.0.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/pin_input_text_field-4.5.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/platform-3.1.6/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/provider-6.1.5/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/pub_semver-2.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/screen_retriever-0.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/screen_retriever_linux-0.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/screen_retriever_macos-0.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/screen_retriever_platform_interface-0.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/screen_retriever_windows-0.2.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/screenshot-3.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/sms_autofill-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/source_span-1.10.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/super_tooltip-2.0.9/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/syncfusion_flutter_charts-29.2.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/syncfusion_flutter_core-29.2.7/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/test_api-0.7.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/timeago-3.7.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/typed_data-1.4.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/vector_graphics-1.1.18/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.17/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/vibration-2.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/vibration_platform_interface-0.0.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/vm_service-15.0.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/web-1.1.1/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/win32-5.13.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/win32_registry-2.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/window_manager-0.4.3/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/xml-6.5.0/lib" />
      <root url="file://$USER_HOME$/.pub-cache/hosted/pub.dev/yaml-3.1.3/lib" />
      <root url="file://$USER_HOME$/development/flutter/bin/cache/pkg/sky_engine/lib" />
      <root url="file://$USER_HOME$/development/flutter/packages/flutter/lib" />
      <root url="file://$USER_HOME$/development/flutter/packages/flutter_localizations/lib" />
      <root url="file://$USER_HOME$/development/flutter/packages/flutter_test/lib" />
      <root url="file://$USER_HOME$/development/flutter/packages/flutter_web_plugins/lib" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>