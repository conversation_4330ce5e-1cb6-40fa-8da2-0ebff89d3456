name: water_meter
description: "Desktop App for Water Meter App"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.8+4

environment:
  sdk: ^3.6.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.3
  intl: any
  connectivity_plus: any
  provider: ^6.0.3 # State Management
  http: any
  flutter_secure_storage: any
  pin_code_fields: ^8.0.1
  loading_animation_widget: ^1.2.1
  flutter_launcher_icons: ^0.14.3
  url_launcher: ^6.2.6
  flutter_switch: ^0.3.2
  dropdown_button2:
    path: ./local_packages/dropdown_button2
  country_code_picker:
    path: ./local_packages/country_code_picker

  flutter_bloc: ^8.1.5
  flutter_svg: ^2.0.10+1
  vibration: ^2.0.0
  flutter_screenutil: ^5.9.3
  google_fonts: ^6.2.1
  clipboard: ^0.1.3
  local_auth: ^2.3.0
  super_tooltip: ^2.0.8
  sms_autofill: ^2.4.0
  device_info_plus: ^11.4.0
  flutter_breadcrumb: ^1.0.1
  excel: ^4.0.6
  open_file: ^3.3.2
  syncfusion_flutter_charts: ^29.2.7
  equatable: any
  syncfusion_flutter_core: any
  path_provider: any
  permission_handler: ^11.3.1
  in_app_update: ^4.2.3
  timeago: ^3.7.0
  device_preview: ^1.2.0
  screenshot: ^3.0.0
  window_manager: ^0.4.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  msix: ^3.16.9

msix_config:
  display_name: "Water Meter: IoT Platform"
  publisher_display_name: Nudron IoT
  identity_name: NudronIoT.WaterMeterIoTPlatform
  publisher: CN=8FE2F31C-E1F8-43B3-9C3B-E15FE39392D0
  msix_version: *******
  store: true
  logo_path: assets/icons/app_icon.png
  capabilities: internetClient

flutter_launcher_icons:
  windows:
    generate: true
    image_path: "assets/icons/app_icon.png"
    icon_size: 48 # min:48, max:256, default: 48
  flutter_test:
    sdk: flutter
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  build_runner: ^2.4.12

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/ca/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package