import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:water_meter/utils/pok.dart';
import 'package:provider/provider.dart';

import '../../../theme/theme2.dart';
import '../../table/fields/ChamferedTextWidget.dart';
import 'customButton.dart';

class ConfirmationDialog extends StatefulWidget {
  ConfirmationDialog({
    super.key,
    required this.heading,
    required this.message,
  });

  String heading;
  String message;

  @override
  State<ConfirmationDialog> createState() => _ConfirmationDialogState();
}

class _ConfirmationDialogState extends State<ConfirmationDialog> {
  @override
  Widget build(BuildContext context) {

    return Dialog(
      insetPadding: const EdgeInsets.all(0),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(0.0),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: Provider.of<ThemeNotifier>(context).currentTheme.dialogBG,
          border: Border.all(
            color:
                Provider.of<ThemeNotifier>(context).currentTheme.gridLineColor,
            width: 4.minSp,
          ),
        ),
        width: 0.35.sw,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              // color: Colors.green,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ChamferedTextWidget2(
                    text: widget.heading.toUpperCase(),
                    borderColor: Provider.of<ThemeNotifier>(context)
                        .currentTheme
                        .gridLineColor,
                  ),
                  IconButton(
                    icon: Icon(Icons.close,
                        color: Provider.of<ThemeNotifier>(context)
                            .currentTheme
                            .gridLineColor),
                    onPressed: () {
                      if (mounted) {
                        Navigator.of(context).pop();
                      }
                    },
                  ),
                ],
              ),
            ),
            SizedBox(height: 20.h),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.w),
              child: Text(
                widget.message,
                textAlign: TextAlign.center,
                style: GoogleFonts.robotoMono(
                  textStyle: TextStyle(
                    fontSize: ThemeNotifier.small.minSp,
                    color: Provider.of<ThemeNotifier>(context)
                        .currentTheme
                        .basicAdvanceTextColor,
                  ),
                ),
              ),
            ),
            SizedBox(height: 20.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                CustomButton(
                  text: "CANCEL",
                  onPressed: () {
                    Navigator.of(context).pop(false);
                  },
                  isRed: true,
                ),
                CustomButton(
                  text: "CONFIRM",
                  onPressed: () {
                    Navigator.of(context).pop(true);
                  },
                ),
              ],
            ),
            SizedBox(height: 20.h),
          ],
        ),
      ),
    );
  }
}