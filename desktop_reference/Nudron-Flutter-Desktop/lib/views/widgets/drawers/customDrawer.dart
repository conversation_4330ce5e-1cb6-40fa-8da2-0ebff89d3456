import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:water_meter/utils/pok.dart';
import 'package:provider/provider.dart';
import 'package:water_meter/views/widgets/drawers/profile/ProfileDrawer.dart';


import '../../../theme/theme2.dart';
import '../containers/CustomAppBar.dart';

//
// class DrawerWithAlert extends StatefulWidget {
//   final int drawerIndex;
//   final List<String> drawerName;
//
//   DrawerWithAlert({
//     required this.drawerIndex,
//     required this.drawerName,
//   });
//
//   @override
//   _DrawerWithAlertState createState() => _DrawerWithAlertState();
// }
//
// class _DrawerWithAlertState extends State<DrawerWithAlert> {
//   bool _showAlert = false;
//   String _alertMessage = "";
//   AlertType _alertType = AlertType.success;
//   List<Widget Function(AlertCallback)> drawerList = [
//     (showAlert) => ProfileDrawer(showAlert: showAlert),
//     // Add more drawer items as needed
//   ];
//
//   void _showCustomAlert(String message, AlertType alertType) {
//     setState(() {
//       _alertMessage = message;
//       _alertType = alertType;
//       _showAlert = true;
//     });
//
//     Future.delayed(Duration(seconds: 2), () {
//       setState(() {
//         _showAlert = false;
//       });
//     });
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Drawer(
//       backgroundColor: Provider.of<ThemeNotifier>(context).currentTheme.bgColor,
//       child: SingleChildScrollView(
//         child: Column(
//           children: [
//             Padding(
//               padding: const EdgeInsets.only(top: 10, left: 10, right: 10),
//               child: Stack(
//                 children: [
//                   Container(),
//                   Align(
//                     alignment: Alignment.topLeft,
//                     child: IconButton(
//                       icon: Icon(
//                         Icons.arrow_back_ios,
//                         color: Provider.of<ThemeNotifier>(context)
//                             .currentTheme
//                             .drawerHeadingColor,
//                         size: 24.minSp,
//                       ),
//                       onPressed: () {
//                         Navigator.pop(context);
//                       },
//                     ),
//                   ),
//                   Padding(
//                     padding: const EdgeInsets.all(8.0),
//                     child: Center(
//                       child: Text(
//                         widget.drawerName[widget.drawerIndex],
//                         style: GoogleFonts.roboto(
//                           fontSize: 24.minSp,
//                           fontWeight: FontWeight.bold,
//                           color: Provider.of<ThemeNotifier>(context)
//                               .currentTheme
//                               .drawerHeadingColor,
//                         ),
//                       ),
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//             widget.drawerIndex >= 1
//                 ? Container()
//                 : drawerList[widget.drawerIndex](_showCustomAlert),
//             if (_showAlert)
//               CustomAlert.customAlertWidget(
//                 _alertMessage,
//                 _alertType, context: context,
//               ),
//           ],
//         ),
//       ),
//     );
//   }
// }

class DrawerWithAlert extends StatefulWidget {
  final int drawerIndex;
  final List<String> drawerName;

  const DrawerWithAlert({super.key, 
    required this.drawerIndex,
    required this.drawerName,
  });

  @override
  _DrawerWithAlertState createState() => _DrawerWithAlertState();
}

class _DrawerWithAlertState extends State<DrawerWithAlert> {
  @override
  Widget build(BuildContext context) {
    List<Widget> list = [
      // ProfileDrawer(),
      const ProfileDrawer(),
    ];
    return SafeArea(
      child: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: Scaffold(
          backgroundColor: Provider.of<ThemeNotifier>(context).currentTheme.bgColor,
          body: Container(
              child: Column(
            children: [
              // CustomAppBar outside the scrollable region
              CustomAppBar(choiceAction: null),

              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      Container(
                        color: Provider.of<ThemeNotifier>(context)
                            .currentTheme
                            .bgColor,
                        child: Padding(
                          padding: EdgeInsets.only(left: 10.w, right: 10.w),
                          child: Stack(
                            children: [
                              Container(),
                              Align(
                                alignment: Alignment.topLeft,
                                child: IconButton(
                                  icon: Icon(
                                    Icons.arrow_back_ios,
                                    color: Provider.of<ThemeNotifier>(context)
                                        .currentTheme
                                        .drawerHeadingColor,
                                    size: 24.minSp,
                                  ),
                                  onPressed: () {
                                    Navigator.pop(context);
                                  },
                                ),
                              ),



                              Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Center(
                                  child: Text(
                                    widget.drawerName[widget.drawerIndex],
                                    style: GoogleFonts.roboto(
                                      fontSize: 24.minSp,
                                      fontWeight: FontWeight.bold,
                                      color:
                                          Provider.of<ThemeNotifier>(context)
                                              .currentTheme
                                              .drawerHeadingColor,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      // Padding(
                      //   padding: const EdgeInsets.all(0.0),
                      //   child: Container(
                      //     height: 1.h,
                      //     color: Color(0xFFB3B3B3),
                      //   ),
                      // ),
                      widget.drawerIndex >= 1 ? Container() : list[widget.drawerIndex],
                    ],
                  ),
                ),
              ),
            ],
          )),
        ),
      ),
    );
  }
}