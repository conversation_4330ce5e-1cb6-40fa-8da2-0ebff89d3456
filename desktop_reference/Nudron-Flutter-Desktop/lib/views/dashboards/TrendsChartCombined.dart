import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:water_meter/utils/pok.dart';

import '../../bloc/dashboardBloc/dashboardBloc.dart';
import '../../bloc/dashboardBloc/dashboardState.dart';
import '../../theme/theme2.dart';
import '../widgets/containers/CustomMultipleSelectorHorizontal.dart';
import 'TrendsChart.dart';
import 'TrendsTable.dart';

class TrendsChartCombined extends StatefulWidget {
  const TrendsChartCombined({super.key});

  @override
  State<TrendsChartCombined> createState() => _TrendsChartCombinedState();
}

class _TrendsChartCombinedState extends State<TrendsChartCombined> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          height: 4.minSp,
          color: CommonColors.yellow,
        ),
        const Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Flexible(
                fit: FlexFit.loose, child: CustomMultipleSelectorHorizontal()),
            // CustomButton(
            //     text: "GET",
            //     dynamicWidth: true,
            //     onPressed: () {
            //       // BlocProvider.of<DashboardBloc>(context).add(GetTrendsData());
            //     })
          ],
        ),
        Container(
          height: 4.minSp,
          color: CommonColors.yellow,
        ),
        Expanded(
          child: BlocBuilder<DashboardBloc, DashboardState>(
              buildWhen: (previous, current) {
            if (current is RefreshDashboard2 || current is RefreshDashboard) {
              return true;
            }
            return false;
          }, builder: (context, state) {
            return TrendsChart(
              key: UniqueKey(),
            );

            // return Container();
          }),
        ),
        Container(
          height: 4.minSp,
          color: CommonColors.yellow,
        ),
        Expanded(
          child: BlocBuilder<DashboardBloc, DashboardState>(
              buildWhen: (previous, current) {
            if (current is RefreshDashboard2 || current is RefreshDashboard) {
              return true;
            }
            return false;
          }, builder: (context, state) {
            return TrendsTable(
                // key: UniqueKey(),
                );
            // return Container();
          }),
        ),
        Container(
          height: 4.minSp,
          color: CommonColors.yellow,
        ),
      ],
    );
  }
}