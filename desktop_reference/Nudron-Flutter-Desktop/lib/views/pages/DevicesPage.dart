import 'dart:async';

import 'package:clipboard/clipboard.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:water_meter/utils/pok.dart';
import 'package:provider/provider.dart';

import '../../bloc/dashboardBloc/dashboardBloc.dart';
import '../../bloc/dashboardBloc/dashboardState.dart';
import '../../config.dart';
import '../../theme/theme2.dart';
import '../table/DataGridWidget.dart';

class DevicesPage extends StatefulWidget {
  const DevicesPage({super.key});

  @override
  State<DevicesPage> createState() => _DevicesPageState();
}

class _DevicesPageState extends State<DevicesPage> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  Timer? _debounce;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _debounce?.cancel();
    _searchController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final dashboardBloc = BlocProvider.of<DashboardBloc>(context);

    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor:
            Provider.of<ThemeNotifier>(context).currentTheme.bgColor,
        body: Container(
          child: Column(
            children: [
              Container(
                height: 4.minSp,
                color: CommonColors.red,
              ),
              Row(
                mainAxisSize: MainAxisSize.max,
                children: [
                  // Container(
                  //   width: 16.minSp,
                  //   color: CommonColors.red,
                  // ),
                  Expanded(
                    child: Material(
                      color: Colors.transparent,
                      child: Ink(
                        color: Provider.of<ThemeNotifier>(context)
                            .currentTheme
                            .dropDownColor,
                        child: InkWell(
                          splashFactory: InkRipple.splashFactory,
                          splashColor:
                              Provider.of<ThemeNotifier>(context, listen: false)
                                  .currentTheme
                                  .splashColor,
                          child: Container(
                            height: 46.h,
                            decoration: BoxDecoration(
                              border: Border(
                                left: BorderSide(
                                  color: CommonColors.red,
                                  width: 12.minSp,
                                ),
                              ),
                            ),
                            child: Padding(
                              padding: EdgeInsets.fromLTRB(12.w, 0.h, 0.w, 0.h),
                              child: Center(
                                child: TextField(
                                  // textAlign: TextAlign
                                  //     .center, // Centers input text & hint horizontally
                                  controller: _searchController,
                                  keyboardType: TextInputType.text,
                                  inputFormatters: [
                                    UpperCaseTextFormatter(),
                                  ],
                                  style: GoogleFonts.robotoMono(
                                    fontSize: ThemeNotifier.small.minSp,
                                    color: Provider.of<ThemeNotifier>(context)
                                        .currentTheme
                                        .basicAdvanceTextColor,
                                  ),
                                  decoration: InputDecoration(
                                    filled: true,
                                    fillColor: Colors.transparent,
                                    hintText: 'SEARCH DEVICE LABEL OR SERIAL NO.',
                                    hintStyle: GoogleFonts.robotoMono(
                                      fontSize: ThemeNotifier.small.minSp,
                                      color: Provider.of<ThemeNotifier>(context)
                                          .currentTheme
                                          .noEntriesColor,
                                    ),
                                    suffixIcon: Padding(
                                      padding: EdgeInsets.only(
                                          right: 8.w), // Adjust padding if needed
                                      child: Icon(
                                        Icons.search,
                                        color: Provider.of<ThemeNotifier>(context)
                                            .currentTheme
                                            .basicAdvanceTextColor,
                                        size: 30.minSp,
                                      ),
                                    ),
                                    border: InputBorder.none,
                                    // Removesg default border

                                    // Key Fix for Vertical Centering
                                    contentPadding: EdgeInsets.symmetric(
                                      vertical: 0,
                                      // horizontal: 4.0,
                                    ),
                                    isCollapsed:
                                        true, // Prevents excessive internal padding
                                  ),
                                  textAlignVertical: TextAlignVertical.center,
                                  // Ensures vertical centering
                                  onChanged: (query) {
                                    if (_debounce?.isActive ?? false)
                                      _debounce!.cancel();

                                    _debounce = Timer(
                                        const Duration(milliseconds: 300), () {
                                      print('Query changed: $query');
                                      setState(() {
                                        dashboardBloc.filterDevices(query);
                                      });
                                    });
                                  },
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  // Expanded(
                  //   child: CustomTextField(
                  //     controller: _searchController,
                  //     hintText: 'Search Device Label or ID',
                  //     hintStyle: GoogleFonts.robotoMono(
                  //       fontSize: ThemeNotifier.medium.minSp,
                  //       // fontWeight: FontWeight.w500,
                  //     ),
                  //     style: GoogleFonts.robotoMono(
                  //       fontSize: ThemeNotifier.medium.minSp,
                  //       // fontWeight: FontWeight.w500,
                  //       color: Provider.of<ThemeNotifier>(context)
                  //           .currentTheme
                  //           .basicAdvanceTextColor,
                  //     ),
                  //     keyboardType: TextInputType.text,
                  //     suffixIcon: Icon(Icons.search),
                  //     onChanged: (query) {
                  //       dashboardBloc.filterDevices(_searchController.text);
                  //     },
                  //     border: OutlineInputBorder(
                  //       borderSide: BorderSide.none,
                  //     ),
                  //     fillColor: Provider.of<ThemeNotifier>(context)
                  //         .currentTheme
                  //         .dropDownColor,
                  //   ),
                  // ),
                ],
              ),
              Container(
                height: 4.minSp,
                color: CommonColors.red,
              ),
              // SizedBox(height: 16),
              Expanded(
                child: BlocBuilder<DashboardBloc, DashboardState>(
                  buildWhen: (previous, current) {
                    if (current is RefreshSummaryPage ||
                        current is RefreshSummaryPage2) {
                      return true;
                    }
                    return false;
                  },
                  builder: (context, state) {
                    return DataGridWidget(
                      data: dashboardBloc.devicesData,
                      key: UniqueKey(),
                      columnsToTakeHeaderWidthAndExtraPadding: {
                        // 0: 0,
                        // 1: 0,
                        3: 0,
                      },
                      frozenColumns: 2,
                      devicesTable: true,
                      location: 'activity',
                    );
                  },
                ),
              ),
              Container(
                height: 3.h,
                color: CommonColors.red,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class UpperCaseTextFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    return TextEditingValue(
      text: newValue.text.toUpperCase(),
      selection: newValue.selection,
    );
  }
}