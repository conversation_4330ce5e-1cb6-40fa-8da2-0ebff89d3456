import 'package:clipboard/clipboard.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:water_meter/utils/pok.dart';
import 'package:provider/provider.dart';

import '../../bloc/dashboardBloc/dashboardBloc.dart';
import '../../bloc/dashboardBloc/dashboardState.dart';
import '../../config.dart';
import '../../model/filterAndSummaryForProject.dart';
import '../../theme/theme2.dart';
import '../../utils/alert_message.dart';
import '../../utils/new_loader.dart';
import '../table/DataGridWidget.dart';
import '../widgets/containers/CustomDropDown.dart';
import '../widgets/containers/customButton.dart';

class ProjectsPage extends StatefulWidget {
  const ProjectsPage({
    super.key,
  });

  @override
  State<ProjectsPage> createState() => _ProjectsPageState();
}

class _ProjectsPageState extends State<ProjectsPage> {
  String? selectedProject;
  List<String?> selectedFilters = [];
  List<String> levels = [];
  FilterAndSummaryForProject? filterData;

  late DashboardBloc dashboardBloc;

  @override
  void initState() {
    super.initState();
    dashboardBloc = BlocProvider.of<DashboardBloc>(context);
    _initializeData();
  }

  void _initializeData() {
    levels = ['Project', ...?dashboardBloc.filterData?.levels];
    selectedFilters = List.filled(levels.length, null);
    selectedFilters[0] = dashboardBloc.currentFilters.firstOrNull;
    for (int i = 1; i < levels.length; i++) {
      selectedFilters[i] = dashboardBloc.currentFilters.elementAtOrNull(i);
    }
    filterData = dashboardBloc.filterData;
  }

  @override
  void dispose() {
    super.dispose();
  }

  void _onFilterChanged(int index, String? value) async {
    // Update the selected filters synchronously

    // If the first filter (project) changes, load the new project data asynchronously
    if (index == 0) {
      try {
        // Show loader while fetching new data
        filterData = await LoaderUtility.showLoader(
          context,
          dashboardBloc.selectProject(dashboardBloc.projects.indexOf(value!)),
        );

        // Once the data is loaded, update the levels and reset the filters
        setState(() {
          levels = ['Project', ...?filterData?.levels];
          selectedFilters = [value, ...List.filled(levels.length - 1, null)];
        });
      } catch (e) {
        // Handle errors
        CustomAlert.showCustomScaffoldMessenger(
          context,
          e.toString(),
          AlertType.error,
        );
      }
    } else {
      if (value == "-") {
        value = null;
      }
      setState(() {
        selectedFilters[index] = value;

        // Clear subsequent filters
        for (int i = index + 1; i < selectedFilters.length; i++) {
          selectedFilters[i] = null;
        }
      });
    }
  }

  void _onConfirm() {
    LoaderUtility.showLoader(
      context,
      dashboardBloc.updateSelectedFilters(selectedFilters, filterData),
    ).catchError((e) {
      CustomAlert.showCustomScaffoldMessenger(
        context,
        e.toString(),
        AlertType.error,
      );
      throw e;
    });
  }

  List<String> _getItemsForLevel(int level, List<String> projects) {
    if (level == 0) {
      return projects;
    } else {
      var items = _getFilterItems(level);

      if (items.isNotEmpty) {
        items = ["-", ...items];
      }
      return items;
    }
  }

  List<String> _getFilterItems(int level) {
    if (selectedFilters[0] == null || filterData == null) {
      return [];
    }

    dynamic currentLevel = filterData!.nestedLevels;

    if (currentLevel == null || level > filterData!.levels.length) {
      return [];
    }

    for (int i = 1; i < level; i++) {
      // Start loop from 1 since we already handled level 0
      var selectedValue = selectedFilters[i];
      if (selectedValue != null && currentLevel != null) {
        currentLevel = currentLevel[selectedValue];
      } else {
        return []; // If selected value is null or invalid, return an empty list
      }
    }

    if (currentLevel is List) {
      return currentLevel.cast<String>();
    }

    if (currentLevel is Map<dynamic, dynamic>) {
      return currentLevel.keys.toList().cast<String>();
    }

    return [];
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor:
        Provider.of<ThemeNotifier>(context).currentTheme.bgColor,
        body: Column(
          children: [
            Container(
              height: 4.minSp,
              color: const Color(0xFF9C27B0),
            ),
            Spacer(),
            Center(
              child: Container(
                width: MediaQuery.of(context).size.width * 0.7, // Constrain width
                constraints: BoxConstraints(
                  maxWidth: 1031, // Maximum width
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [

                    SizedBox(height: 40.h), // Add space at top
                    Container(
                      padding: EdgeInsets.symmetric(vertical: 20.h),
                      alignment: Alignment.center,
                      child: CustomDropdownButton2(
                        fieldName: "Select Project",
                        value: selectedFilters.first,
                        items: dashboardBloc.projects,
                        onChanged: (value) => _onFilterChanged(0, value),
                      ),
                    ),
                    SizedBox(height: 20.h),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 40),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          CustomButton(
                            text: 'CONFIRM',
                            onPressed: _onConfirm,
                            width: 150,
                          ),
                          SizedBox(width: 20.w),
                        ],
                      ),
                    ),
                    SizedBox(height: 40.h), // Add space at bottom

                  ],
                ),
              ),
            ),
            Spacer(),
            Container(
              height: 3.h,
              color: const Color(0xFF9C27B0),
            ),
          ],
        ),
      ),
    );
  }
}