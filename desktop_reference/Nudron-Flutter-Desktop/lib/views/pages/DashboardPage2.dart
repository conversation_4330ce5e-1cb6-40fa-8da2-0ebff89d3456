import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:water_meter/utils/pok.dart';
import 'package:provider/provider.dart';

import '../../bloc/dashboardBloc/dashboardState.dart';
import '../../bloc/dashboardBloc/dashboardBloc.dart';
import '../../theme/theme2.dart';
import '../../utils/alert_message.dart';
import '../../utils/loader.dart';
import '../../utils/new_loader.dart';
import '../dashboards/SummaryTable.dart';
import '../dashboards/TrendsChartCombined.dart';
import '../widgets/containers/CustomAppBar.dart';
import '../widgets/containers/customButton.dart';
import '../widgets/drawers/profile/ProfileDrawer.dart';
import 'BackgroundChart.dart';
import 'DevicesPage.dart';
import 'ProjectsPage.dart';

class DashboardPage extends StatefulWidget {
  const DashboardPage({super.key});

  @override
  _DashboardPageState createState() => _DashboardPageState();
}

class _DashboardPageState extends State<DashboardPage> {
  int drawerIndex = 0;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var fullScreens = [
      MainDashboardPage(),
      BackgroundChart(),
    ];

    return MultiProvider(
      providers: [
        BlocProvider(create: (context) => DashboardBloc()),
      ],
      child: SafeArea(
        child: Scaffold(
          resizeToAvoidBottomInset: false,
          body: BlocBuilder<DashboardBloc, DashboardState>(
              buildWhen: (previous, current) {
            if ((current is DashboardPageError ||
                current is DashboardPageLoaded ||
                current is ChangeScreen)) {
              return true;
            }
            return false;
          }, builder: (context, state) {
            if (state is DashboardPageLoaded || state is ChangeScreen) {
              return GestureDetector(
                  onTap: () => FocusScope.of(context).unfocus(),
                  child: IndexedStack(
                    index: BlocProvider.of<DashboardBloc>(context).screenIndex,
                    children: fullScreens,
                  ));
            } else if (state is DashboardPageError) {
              return Scaffold(
                body: Center(
                  child: SizedBox(
                    height: 600.h,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Text("ERROR IN FETCHING DATA. REFRESH LATER",
                            style: GoogleFonts.roboto(
                              color: CommonColors.blue,
                              fontSize: ThemeNotifier.medium.minSp,
                              fontWeight: FontWeight.w500,
                            )),
                        const SizedBox(height: 20),
                        CustomButton(
                          text: "REFRESH",
                          onPressed: () {
                            LoaderUtility.showLoader(
                                    context,
                                    BlocProvider.of<DashboardBloc>(context)
                                        .loadInitialData())
                                .then((s) {})
                                .catchError((e) {
                              CustomAlert.showCustomScaffoldMessenger(context,
                                  "Error in loading data", AlertType.error);
                            });
                          },
                        )
                      ],
                    ),
                  ),
                ),
              );
            }
            return const CustomLoader();
          }),
        ),
      ),
    );
  }
}

class MainDashboardPage extends StatefulWidget {
  static List<String> bottomNavTabs = [
    'trends',
    'billing',
    'activity',
    'profile',
  ];

  const MainDashboardPage({super.key});

  @override
  State<MainDashboardPage> createState() => _MainDashboardPageState();
}

class _MainDashboardPageState extends State<MainDashboardPage> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  int drawerIndex = 0;

  final double _drawerWidth = 230.0;
  bool _isDrawerCollapsed = true;

  List<Color> selectedColor = [
    const Color(0xFFDFAC46),
    CommonColors.green,
    CommonColors.red,
    CommonColors.blue2
  ];

  static List<String> bottomNavTabIcons = [
    'trends',
    'billing',
    'activity',
    'profile',
  ];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback(
      (_) async {
        MainDashboardPage.bottomNavTabs =
            (await DashboardBloc.updateBottomNavTabs(
                project: BlocProvider.of<DashboardBloc>(context)
                    .currentFilters
                    .firstOrNull))!;

        drawerIndex = BlocProvider.of<DashboardBloc>(context).bottomNavPos;
      },
    );
  }

  Widget _buildDesktopSideNav(BuildContext context, int currentNavPos) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 100),
      width: _isDrawerCollapsed ? 130.0.w : _drawerWidth,
      decoration: BoxDecoration(
        color: Provider.of<ThemeNotifier>(context).currentTheme.bgColor,
        border: Border(
          right: BorderSide(
            color: Colors.grey.withOpacity(0.2),
            width: 1.0,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: _isDrawerCollapsed
            ? CrossAxisAlignment.center
            : CrossAxisAlignment.start,
        children: [
          Container(
            height: 3.h,
            color: selectedColor[drawerIndex],
          ),
          // Header with toggle button

          Divider(height: 1, thickness: 1, color: Colors.grey.withOpacity(0.2)),

          // Navigation items
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: List.generate(
                MainDashboardPage.bottomNavTabs.length,
                (index) => _buildNavItem(
                  index: index,
                  isSelected: currentNavPos == index,
                  context: context,
                  isCollapsed: _isDrawerCollapsed,
                  currentIndex: currentNavPos,
                ),
              ),
            ),
          ),

          Container(
            height: 3.h,
            color: selectedColor[drawerIndex  ],
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem({
    required int index,
    required bool isSelected,
    required BuildContext context,
    required bool isCollapsed,
    required int currentIndex,
  }) {

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          BlocProvider.of<DashboardBloc>(context).switchBottomNavPos(index);
          setState(() {
            drawerIndex = index;
          });
        },
        child: Container(
          height: 86.0.w,
          decoration: BoxDecoration(
            color: isSelected
                ? selectedColor[index].withOpacity(0.1)
                : Colors.transparent,
            border: Border(
              left: BorderSide(
                color: isSelected ? selectedColor[index] : Colors.transparent,
                width: 4.0,
              ),
            ),
          ),
          padding: EdgeInsets.symmetric(
            horizontal: isCollapsed ? 8.0.w : 20.0.w,
          ),
          child: isCollapsed
              ? Column(
            mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Center(
                    child: SvgPicture.asset(
                      "assets/icons/${bottomNavTabIcons[index]}.svg",
                      color: isSelected
                          ? selectedColor[index]
                          : Provider.of<ThemeNotifier>(context)
                              .currentTheme
                              .inactiveBottomNavbarIconColor,
                      width: 50.0.w,
                      height: 50.0.w,
                    ),
                  ),
                  FittedBox(
                    fit: BoxFit.scaleDown,
                    child: Text(
                      MainDashboardPage.bottomNavTabs[index]
                          .toUpperCase(),
                      style: GoogleFonts.robotoMono(
                        color: currentIndex == index
                            ? selectedColor[index]
                            : Provider.of<ThemeNotifier>(context)
                            .currentTheme
                            .inactiveBottomNavbarIconColor,
                        fontSize: 20.minSp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              )
              : Row(
                  children: [
                    SvgPicture.asset(
                      "assets/icons/${bottomNavTabIcons[index]}.svg",
                      color: isSelected
                          ? selectedColor[index]
                          : Provider.of<ThemeNotifier>(context)
                              .currentTheme
                              .inactiveBottomNavbarIconColor,
                      width: 40.0,
                      height: 40.0,
                    ),
                    const SizedBox(width: 16.0),
                    Expanded(
                      child: Text(
                        MainDashboardPage.bottomNavTabs[index].toUpperCase(),
                        style: GoogleFonts.robotoMono(
                          color: isSelected
                              ? selectedColor[index]
                              : Provider.of<ThemeNotifier>(context)
                                  .currentTheme
                                  .inactiveBottomNavbarIconColor,
                          fontSize: 18.0,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> contentPages = [
      TrendsChartCombined(key: UniqueKey()),
      SummaryTable(key: UniqueKey()),
      DevicesPage(),
      ProfileDrawer(),
    ];

    return WillPopScope(
      onWillPop: () {
        return Future.value(false);
      },
      child: Focus(
        autofocus: true,
        child: Scaffold(
          key: _scaffoldKey,
          backgroundColor:
              Provider.of<ThemeNotifier>(context).currentTheme.bgColor,
          appBar: CustomAppBar(choiceAction: null),
          body: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Desktop side navigation
              BlocBuilder<DashboardBloc, DashboardState>(
                buildWhen: (previous, current) => current is ChangeDashBoardNav,
                builder: (context, state) {
                  int currentNavPos =
                      BlocProvider.of<DashboardBloc>(context).bottomNavPos;
                  return _buildDesktopSideNav(context, currentNavPos);
                },
              ),
              Container(
                width: 3.minSp,
                color: selectedColor[BlocProvider.of<DashboardBloc>(context).bottomNavPos],
              ),
              // Main content area
              Expanded(
                child: BlocBuilder<DashboardBloc, DashboardState>(
                    buildWhen: (previous, current) =>
                        current is ChangeDashBoardNav,
                    builder: (context, state) {
                      int currentNavPos =
                          BlocProvider.of<DashboardBloc>(context).bottomNavPos;
                      return IndexedStack(
                        index: currentNavPos,
                        children: contentPages,
                      );
                    }),
              ),
            ],
          ),
        ),
      ),
    );
  }
}