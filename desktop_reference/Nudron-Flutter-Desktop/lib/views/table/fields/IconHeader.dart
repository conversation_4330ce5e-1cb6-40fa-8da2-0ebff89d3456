import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:water_meter/utils/pok.dart';
import 'package:provider/provider.dart';

import '../../../theme/theme2.dart';
import '../../../view_model/data_post_requests.dart';

class HeaderWidget extends StatelessWidget {
  final String title;

  const HeaderWidget({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: title[0] == '!'
          ? EdgeInsets.zero
          : EdgeInsets.symmetric(horizontal: 4),
      child: title[0] != '!'
          ? Text(
              title,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: GoogleFonts.robotoMono(
                fontSize: ThemeNotifier.medium.minSp,
                fontWeight: FontWeight.bold,
                color: Provider.of<ThemeNotifier>(context)
                    .currentTheme
                    .gridHeadingColor,
                height: 1.5,
                // Adjusting line height for consistency
                letterSpacing:
                    0.5, // Ensure this matches any spacing used in your Text widget
              ),
            )
          : Row(
              children: [
                CustomIconButton(
                  tooltipMessage: title[1] == 'A'
                      ? DataPostRequests.getIconNamesForAlerts(
                          int.parse(title[3]))
                      : DataPostRequests.getIconNamesForStatus(
                          int.parse(title[3])),
                  iconAsset:
                      'assets/icons/${title[1].toLowerCase()}${title[3]}.svg',
                  onTap: () {
                    // Do something
                  },
                ),
              ],
            ),
    );
  }
}

class CustomIconButton extends StatelessWidget {
  final String tooltipMessage;
  final String iconAsset;
  final VoidCallback onTap;

  // Static variable to keep track of the active overlay across instances
  static OverlayEntry? _currentOverlayEntry;

  const CustomIconButton({
    super.key,
    required this.tooltipMessage,
    required this.iconAsset,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: GestureDetector(
        onTap: () {
          _showCustomTooltip(context);
          onTap();
        },
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () {
              // Call the same onTap as GestureDetector
              _showCustomTooltip(context);
              onTap();
            },
            splashColor: Provider.of<ThemeNotifier>(context, listen: false)
                .currentTheme
                .splashColor,
            highlightColor: Provider.of<ThemeNotifier>(context, listen: false)
                .currentTheme
                .splashColor,
            child: Container(
              height: 30.h,
              decoration: BoxDecoration(
                color: Colors.transparent,
                borderRadius: BorderRadius.circular(2.minSp),
              ),
              child: SvgPicture.asset(
                iconAsset,
                width: 25.minSp,
                height: 25.minSp,
                color: Provider.of<ThemeNotifier>(context, listen: false)
                    .currentTheme
                    .gridHeadingColor,
                semanticsLabel: 'Icon',
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _showCustomTooltip(BuildContext context) {
    // Remove any existing tooltip before showing the new one
    _removeExistingTooltip();

    // Find the position of the icon on the screen
    final renderBox = context.findRenderObject() as RenderBox;
    final position = renderBox.localToGlobal(Offset.zero);

    // Calculate the width of the tooltip text
    final textPainter = TextPainter(
      text: TextSpan(
        text: tooltipMessage,
        style: GoogleFonts.robotoMono(
          fontSize: ThemeNotifier.medium.minSp,
          color: Provider.of<ThemeNotifier>(context, listen: false)
              .currentTheme
              .gridHeadingColor,
        ),
      ),
      textDirection: TextDirection.ltr,
    )..layout();

    final tooltipWidth = textPainter.width;
    final screenWidth = MediaQuery.of(context).size.width;

    // Calculate the horizontal offset for the tooltip
    double halfTooltipWidth = tooltipWidth / 2;
    double fullTooltipWidth = tooltipWidth;

    // Calculate the start point for the tooltip
    double leftOffset = position.dx - halfTooltipWidth;
    leftOffset = leftOffset < 0
        ? 8.0
        : leftOffset; // Ensure it doesn't go off the left edge

    // Adjust if it goes beyond the screen width
    if (leftOffset + fullTooltipWidth + 25.minSp > screenWidth) {
      leftOffset = screenWidth - fullTooltipWidth - 25.minSp;
    }

    // Create and insert the new overlay
    final overlay = Overlay.of(context);
    _currentOverlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: position.dy + 40.h, // Adjust this value as needed
        left: leftOffset,
        child: Material(
          color: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.all(8.0),
            decoration: BoxDecoration(
              color: Provider.of<ThemeNotifier>(context).currentTheme.dialogBG,
              borderRadius: BorderRadius.circular(4.0),
            ),
            child: Text(
              tooltipMessage,
              style: GoogleFonts.robotoMono(
                fontSize: ThemeNotifier.medium.minSp,
                color: Provider.of<ThemeNotifier>(context)
                    .currentTheme
                    .gridHeadingColor,
              ),
            ),
          ),
        ),
      ),
    );

    overlay.insert(_currentOverlayEntry!);

    Future.delayed(const Duration(seconds: 2), () {
      _removeExistingTooltip();
    });
  }

  static void _removeExistingTooltip() {
    // If there is a current overlay, remove it
    if (_currentOverlayEntry != null) {
      _currentOverlayEntry!.remove();
      _currentOverlayEntry = null;
    }
  }
}